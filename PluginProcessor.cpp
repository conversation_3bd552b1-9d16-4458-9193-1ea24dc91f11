#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
// Custom DSP Module Implementations
//==============================================================================

// AGC Processor Implementation
void AGCProcessor::prepare(const juce::dsp::ProcessSpec& spec)
{
    sampleRate = spec.sampleRate;
    ballistics.prepare(spec);
    ballistics.setLevelCalculationType(juce::dsp::BallisticsFilterLevelCalculationType::RMS);
    ballistics.setAttackTime(responseTime);
    ballistics.setReleaseTime(responseTime * 2.0f);
    reset();
}

void AGCProcessor::reset()
{
    ballistics.reset();
    currentGain = 1.0f;
    currentGainReduction = 0.0f;
}

void AGCProcessor::setTargetLevel(float targetLevelDb)
{
    targetLevel = targetLevelDb;
}

void AGCProcessor::setResponseTime(float responseTimeMs)
{
    responseTime = responseTimeMs;
    ballistics.setAttackTime(responseTimeMs);
    ballistics.setReleaseTime(responseTimeMs * 2.0f);
}

void AGCProcessor::process(juce::dsp::AudioBlock<float>& block)
{
    auto numChannels = block.getNumChannels();
    auto numSamples = block.getNumSamples();

    for (size_t channel = 0; channel < numChannels; ++channel)
    {
        auto* channelData = block.getChannelPointer(channel);

        for (size_t sample = 0; sample < numSamples; ++sample)
        {
            float inputLevel = std::abs(channelData[sample]);
            float levelDb = juce::Decibels::gainToDecibels(inputLevel, -60.0f);

            float targetGain = juce::Decibels::decibelsToGain(targetLevel - levelDb);
            targetGain = juce::jlimit(0.1f, 10.0f, targetGain);

            currentGain = ballistics.processSample(0, targetGain);
            channelData[sample] *= currentGain;
        }
    }

    currentGainReduction = juce::Decibels::gainToDecibels(1.0f / currentGain, 0.0f);
}

// De-esser Processor Implementation
void DeEsserProcessor::prepare(const juce::dsp::ProcessSpec& spec)
{
    sampleRate = spec.sampleRate;

    // Setup filters for sibilance detection
    auto highpassCoeffs = juce::dsp::IIR::Coefficients<float>::makeHighPass(sampleRate, frequency * 0.8f);
    auto lowpassCoeffs = juce::dsp::IIR::Coefficients<float>::makeLowPass(sampleRate, frequency * 1.2f);

    highpassFilter.coefficients = highpassCoeffs;
    lowpassFilter.coefficients = lowpassCoeffs;

    highpassFilter.prepare(spec);
    lowpassFilter.prepare(spec);

    sibilanceCompressor.prepare(spec);
    sibilanceCompressor.setRatio(4.0f);
    sibilanceCompressor.setAttack(0.5f);
    sibilanceCompressor.setRelease(50.0f);

    reset();
}

void DeEsserProcessor::reset()
{
    highpassFilter.reset();
    lowpassFilter.reset();
    sibilanceCompressor.reset();
    currentReduction = 0.0f;
}

void DeEsserProcessor::setFrequency(float frequencyHz)
{
    frequency = frequencyHz;
    auto highpassCoeffs = juce::dsp::IIR::Coefficients<float>::makeHighPass(sampleRate, frequency * 0.8f);
    auto lowpassCoeffs = juce::dsp::IIR::Coefficients<float>::makeLowPass(sampleRate, frequency * 1.2f);

    highpassFilter.coefficients = highpassCoeffs;
    lowpassFilter.coefficients = lowpassCoeffs;
}

void DeEsserProcessor::setThreshold(float thresholdDb)
{
    threshold = thresholdDb;
    sibilanceCompressor.setThreshold(thresholdDb);
}

void DeEsserProcessor::setReduction(float reductionDb)
{
    reduction = reductionDb;
}

void DeEsserProcessor::process(juce::dsp::AudioBlock<float>& block)
{
    // Create a copy for sibilance detection
    juce::AudioBuffer<float> detectionBuffer(static_cast<int>(block.getNumChannels()),
                                           static_cast<int>(block.getNumSamples()));

    for (size_t channel = 0; channel < block.getNumChannels(); ++channel)
    {
        auto* src = block.getChannelPointer(channel);
        auto* dst = detectionBuffer.getWritePointer(static_cast<int>(channel));
        std::copy(src, src + block.getNumSamples(), dst);
    }

    // Filter for sibilance detection
    juce::dsp::AudioBlock<float> detectionBlock(detectionBuffer);
    juce::dsp::ProcessContextReplacing<float> detectionContext(detectionBlock);

    highpassFilter.process(detectionContext);
    lowpassFilter.process(detectionContext);

    // Apply compression based on sibilance detection
    sibilanceCompressor.process(detectionContext);

    // Calculate reduction amount and apply to original signal
    float reductionGain = juce::Decibels::decibelsToGain(-reduction);

    for (size_t channel = 0; channel < block.getNumChannels(); ++channel)
    {
        auto* originalData = block.getChannelPointer(channel);
        auto* detectionData = detectionBuffer.getReadPointer(static_cast<int>(channel));

        for (size_t sample = 0; sample < block.getNumSamples(); ++sample)
        {
            float detectionLevel = std::abs(detectionData[sample]);
            float compressionAmount = juce::jmax(0.0f, detectionLevel - juce::Decibels::decibelsToGain(threshold));
            float reductionAmount = compressionAmount * (1.0f - reductionGain);

            originalData[sample] *= (1.0f - reductionAmount);
        }
    }

    currentReduction = reduction * 0.5f; // Simplified metering
}

// Multiband Compressor Implementation
void MultibandCompressor::prepare(const juce::dsp::ProcessSpec& spec)
{
    lowMidCrossover.prepare(spec);
    midHighCrossover.prepare(spec);

    for (auto& compressor : bandCompressors)
        compressor.prepare(spec);

    for (auto& gain : bandGains)
        gain.prepare(spec);

    setCrossoverFrequencies(250.0f, 2500.0f);
    reset();
}

void MultibandCompressor::reset()
{
    lowMidCrossover.reset();
    midHighCrossover.reset();

    for (auto& compressor : bandCompressors)
        compressor.reset();

    for (auto& gain : bandGains)
        gain.reset();

    std::fill(bandGainReduction.begin(), bandGainReduction.end(), 0.0f);
}

void MultibandCompressor::setEnabled(bool enabled)
{
    isEnabled = enabled;
}

void MultibandCompressor::setCrossoverFrequencies(float lowMid, float midHigh)
{
    lowMidCrossover.setCutoffFrequency(lowMid);
    midHighCrossover.setCutoffFrequency(midHigh);
}

void MultibandCompressor::setBandParameters(int band, float threshold, float ratio, float attack, float release, float makeupGain)
{
    if (band >= 0 && band < numBands)
    {
        bandCompressors[band].setThreshold(threshold);
        bandCompressors[band].setRatio(ratio);
        bandCompressors[band].setAttack(attack);
        bandCompressors[band].setRelease(release);
        bandGains[band].setGainDecibels(makeupGain);
    }
}

void MultibandCompressor::process(juce::dsp::AudioBlock<float>& block)
{
    if (!isEnabled)
        return;

    // Split into bands using crossover filters
    auto lowBand = block;
    auto midBand = block;
    auto highBand = block;

    juce::dsp::ProcessContextReplacing<float> lowContext(lowBand);
    juce::dsp::ProcessContextReplacing<float> midContext(midBand);
    juce::dsp::ProcessContextReplacing<float> highContext(highBand);

    // Apply crossover filtering (simplified approach)
    lowMidCrossover.process(lowContext);
    midHighCrossover.process(midContext);

    // Process each band
    bandCompressors[0].process(lowContext);
    bandCompressors[1].process(midContext);
    bandCompressors[2].process(highContext);

    bandGains[0].process(lowContext);
    bandGains[1].process(midContext);
    bandGains[2].process(highContext);

    // Sum bands back together (simplified)
    for (size_t channel = 0; channel < block.getNumChannels(); ++channel)
    {
        auto* blockData = block.getChannelPointer(channel);
        auto* lowData = lowBand.getChannelPointer(channel);
        auto* midData = midBand.getChannelPointer(channel);
        auto* highData = highBand.getChannelPointer(channel);

        for (size_t sample = 0; sample < block.getNumSamples(); ++sample)
        {
            blockData[sample] = (lowData[sample] + midData[sample] + highData[sample]) / 3.0f;
        }
    }

    // Update gain reduction metering (simplified)
    for (int i = 0; i < numBands; ++i)
    {
        bandGainReduction[i] = 2.0f; // Simplified metering
    }
}

float MultibandCompressor::getBandGainReduction(int band) const
{
    if (band >= 0 && band < numBands)
        return bandGainReduction[band];
    return 0.0f;
}

//==============================================================================
// Enhanced Preset definitions with comprehensive parameters
const std::array<CompliAudioProcessor::PresetData, 5> CompliAudioProcessor::presets = {{
    // Voice Optimized: 3:1 ratio, 4-5dB GR, 5-10ms attack, 50-60ms release, 2-3dB makeup, -3dB limiter
    {
        -18.0f, 1000.0f, false,                    // AGC: target level, response time, enabled
        -40.0f, 5.0f, 100.0f, 200.0f, false,      // Gate: threshold, attack, hold, release, enabled
        -18.0f, 3.0f, 8.0f, 55.0f, 2.5f, false,   // Compressor: threshold, ratio, attack, release, makeup, multiband
        250.0f, 2500.0f,                          // Crossover frequencies
        6000.0f, -12.0f, 6.0f, false,             // De-esser: freq, threshold, reduction, enabled
        -3.0f, 1.0f, 50.0f, true,                 // Limiter: threshold, attack, release, enabled
        "Voice Optimized"
    },

    // Broadcast: 4:1-6:1 ratio, 6-8dB GR, 3-5ms attack, 50-100ms release, -1 to -2dB limiter, with AGC
    {
        -16.0f, 800.0f, true,                     // AGC: target level, response time, enabled
        -45.0f, 3.0f, 50.0f, 150.0f, false,      // Gate: threshold, attack, hold, release, enabled
        -20.0f, 5.0f, 4.0f, 75.0f, 6.0f, false,  // Compressor: threshold, ratio, attack, release, makeup, multiband
        250.0f, 2500.0f,                         // Crossover frequencies
        6000.0f, -12.0f, 6.0f, false,            // De-esser: freq, threshold, reduction, enabled
        -1.5f, 1.0f, 50.0f, true,                // Limiter: threshold, attack, release, enabled
        "Broadcast"
    },

    // Podcast: -16 LUFS target, 3:1-4:1 ratio, 3-5dB GR, 5-8ms attack, 40-50ms release, -3 to -6dB limiter, with de-esser
    {
        -16.0f, 1200.0f, false,                   // AGC: target level, response time, enabled
        -50.0f, 8.0f, 200.0f, 300.0f, false,     // Gate: threshold, attack, hold, release, enabled
        -16.0f, 3.5f, 6.5f, 45.0f, 4.0f, false,  // Compressor: threshold, ratio, attack, release, makeup, multiband
        250.0f, 2500.0f,                         // Crossover frequencies
        5500.0f, -10.0f, 4.0f, true,             // De-esser: freq, threshold, reduction, enabled
        -4.5f, 2.0f, 100.0f, true,               // Limiter: threshold, attack, release, enabled
        "Podcast"
    },

    // Gaming: 4:1-6:1 ratio, 3-6dB GR, 1-3ms attack, 30-50ms release, with aggressive noise gate
    {
        -18.0f, 500.0f, false,                    // AGC: target level, response time, enabled
        -35.0f, 1.0f, 20.0f, 50.0f, true,        // Gate: threshold, attack, hold, release, enabled
        -15.0f, 5.0f, 2.0f, 40.0f, 4.5f, false,  // Compressor: threshold, ratio, attack, release, makeup, multiband
        250.0f, 2500.0f,                         // Crossover frequencies
        6000.0f, -12.0f, 6.0f, false,            // De-esser: freq, threshold, reduction, enabled
        -2.0f, 0.5f, 30.0f, true,                // Limiter: threshold, attack, release, enabled
        "Gaming"
    },

    // Custom: Full parameter access with extended ranges for all modules
    {
        -18.0f, 1000.0f, false,                   // AGC: target level, response time, enabled
        -40.0f, 5.0f, 100.0f, 200.0f, false,     // Gate: threshold, attack, hold, release, enabled
        -18.0f, 3.0f, 8.0f, 55.0f, 2.5f, false,  // Compressor: threshold, ratio, attack, release, makeup, multiband
        250.0f, 2500.0f,                         // Crossover frequencies
        6000.0f, -12.0f, 6.0f, false,            // De-esser: freq, threshold, reduction, enabled
        -3.0f, 1.0f, 50.0f, true,                // Limiter: threshold, attack, release, enabled
        "Custom"
    }
}};

CompliAudioProcessor::CompliAudioProcessor()
     : AudioProcessor (BusesProperties()
                     #if ! JucePlugin_IsMidiEffect
                      #if ! JucePlugin_IsSynth
                       .withInput  ("Input",  juce::AudioChannelSet::stereo(), true)
                      #endif
                       .withOutput ("Output", juce::AudioChannelSet::stereo(), true)
                     #endif
                       )
{
    // Initialize audio device manager for standalone use
    audioDeviceManager.initialiseWithDefaultDevices(2, 2);
}

CompliAudioProcessor::~CompliAudioProcessor()
{
}

//==============================================================================
const juce::String CompliAudioProcessor::getName() const
{
    return JucePlugin_Name;
}

bool CompliAudioProcessor::acceptsMidi() const
{
   #if JucePlugin_WantsMidiInput
    return true;
   #else
    return false;
   #endif
}

bool CompliAudioProcessor::producesMidi() const
{
   #if JucePlugin_ProducesMidiOutput
    return true;
   #else
    return false;
   #endif
}

bool CompliAudioProcessor::isMidiEffect() const
{
   #if JucePlugin_IsMidiEffect
    return true;
   #else
    return false;
   #endif
}

double CompliAudioProcessor::getTailLengthSeconds() const
{
    return 0.0;
}

int CompliAudioProcessor::getNumPrograms()
{
    return 1;   // NB: some hosts don't cope very well if you tell them there are 0 programs,
                // so this should be at least 1, even if you're not really implementing programs.
}

int CompliAudioProcessor::getCurrentProgram()
{
    return 0;
}

void CompliAudioProcessor::setCurrentProgram (int index)
{
    juce::ignoreUnused (index);
}

const juce::String CompliAudioProcessor::getProgramName (int index)
{
    juce::ignoreUnused (index);
    return {};
}

void CompliAudioProcessor::changeProgramName (int index, const juce::String& newName)
{
    juce::ignoreUnused (index, newName);
}

//==============================================================================
void CompliAudioProcessor::prepareToPlay (double sampleRate, int samplesPerBlock)
{
    // Use this method as the place to do any pre-playback
    // initialisation that you need..
    juce::dsp::ProcessSpec spec;
    spec.sampleRate = sampleRate;
    spec.maximumBlockSize = static_cast<juce::uint32>(samplesPerBlock);
    spec.numChannels = static_cast<juce::uint32>(getTotalNumOutputChannels());

    // Prepare all DSP modules in the comprehensive processing chain
    agcProcessor.prepare(spec);
    noiseGate.prepare(spec);
    compressor.prepare(spec);
    multibandCompressor.prepare(spec);
    deEsserProcessor.prepare(spec);
    limiter.prepare(spec);

    // Initialize AGC with default settings
    agcProcessor.setTargetLevel(-18.0f);
    agcProcessor.setResponseTime(1000.0f);

    // Initialize noise gate with default settings
    noiseGate.setThreshold(-40.0f);
    noiseGate.setAttack(5.0f);
    noiseGate.setRelease(200.0f);
    // Note: JUCE NoiseGate doesn't have setHold method

    // Initialize compressor with default settings
    compressor.setThreshold(-18.0f);
    compressor.setRatio(3.0f);
    compressor.setAttack(8.0f);
    compressor.setRelease(55.0f);

    // Initialize multiband compressor
    multibandCompressor.setCrossoverFrequencies(250.0f, 2500.0f);
    multibandCompressor.setEnabled(false);

    // Initialize de-esser with default settings
    deEsserProcessor.setFrequency(6000.0f);
    deEsserProcessor.setThreshold(-12.0f);
    deEsserProcessor.setReduction(6.0f);

    // Initialize limiter with default settings
    limiter.setThreshold(-3.0f);
    limiter.setRelease(50.0f);
    // Note: JUCE Limiter doesn't have setAttack method
}

void CompliAudioProcessor::releaseResources()
{
    // When playback stops, you can use this as an opportunity to free up any
    // spare memory, etc.
    agcProcessor.reset();
    noiseGate.reset();
    compressor.reset();
    multibandCompressor.reset();
    deEsserProcessor.reset();
    limiter.reset();
}

bool CompliAudioProcessor::isBusesLayoutSupported (const BusesLayout& layouts) const
{
  #if JucePlugin_IsMidiEffect
    juce::ignoreUnused (layouts);
    return true;
  #else
    // This is the place where you check if the layout is supported.
    // In this template code we only support mono or stereo.
    // Some plugin hosts, such as certain GarageBand versions, will only
    // load plugins that support stereo bus layouts.
    if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::mono()
     && layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
        return false;

    // This checks if the input layout matches the output layout
   #if ! JucePlugin_IsSynth
    if (layouts.getMainOutputChannelSet() != layouts.getMainInputChannelSet())
        return false;
   #endif

    return true;
  #endif
}

void CompliAudioProcessor::processBlock (juce::AudioBuffer<float>& buffer,
                                              juce::MidiBuffer& midiMessages)
{
    juce::ignoreUnused (midiMessages);

    juce::ScopedNoDenormals noDenormals;
    auto totalNumInputChannels  = getTotalNumInputChannels();
    auto totalNumOutputChannels = getTotalNumOutputChannels();

    // In case we have more outputs than inputs, this code clears any output
    // channels that didn't contain input data, (because these aren't
    // guaranteed to be empty - they may contain garbage).
    for (auto i = totalNumInputChannels; i < totalNumOutputChannels; ++i)
        buffer.clear (i, 0, buffer.getNumSamples());

    // Calculate input level for metering
    float inputRMS = 0.0f;
    for (int channel = 0; channel < totalNumInputChannels; ++channel)
    {
        auto* channelData = buffer.getReadPointer(channel);
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            inputRMS += channelData[sample] * channelData[sample];
        }
    }
    inputRMS = std::sqrt(inputRMS / (buffer.getNumSamples() * totalNumInputChannels));
    inputLevel.store(juce::Decibels::gainToDecibels(inputRMS, -60.0f));

    // Check for preset changes
    int newPresetIndex = static_cast<int>(apvts.getRawParameterValue("preset")->load());
    if (newPresetIndex != currentPresetIndex && newPresetIndex < 4) // Don't auto-load Custom preset
    {
        currentPresetIndex = newPresetIndex;
        loadPreset(currentPresetIndex);
    }

    // Update all DSP module parameters from APVTS
    // AGC parameters
    agcProcessor.setTargetLevel(apvts.getRawParameterValue("agcTargetLevel")->load());
    agcProcessor.setResponseTime(apvts.getRawParameterValue("agcResponseTime")->load());

    // Noise Gate parameters
    noiseGate.setThreshold(apvts.getRawParameterValue("gateThreshold")->load());
    noiseGate.setAttack(apvts.getRawParameterValue("gateAttack")->load());
    noiseGate.setRelease(apvts.getRawParameterValue("gateRelease")->load());
    // Note: JUCE NoiseGate doesn't have setHold method

    // Compressor parameters
    compressor.setThreshold(apvts.getRawParameterValue("threshold")->load());
    compressor.setRatio(apvts.getRawParameterValue("ratio")->load());
    compressor.setAttack(apvts.getRawParameterValue("attack")->load());
    compressor.setRelease(apvts.getRawParameterValue("release")->load());

    // Multiband compressor
    multibandCompressor.setEnabled(apvts.getRawParameterValue("multibandEnabled")->load() > 0.5f);
    multibandCompressor.setCrossoverFrequencies(
        apvts.getRawParameterValue("lowMidCrossover")->load(),
        apvts.getRawParameterValue("midHighCrossover")->load());

    // De-esser parameters
    deEsserProcessor.setFrequency(apvts.getRawParameterValue("deEsserFreq")->load());
    deEsserProcessor.setThreshold(apvts.getRawParameterValue("deEsserThreshold")->load());
    deEsserProcessor.setReduction(apvts.getRawParameterValue("deEsserReduction")->load());

    // Limiter parameters
    limiter.setThreshold(apvts.getRawParameterValue("limiterThreshold")->load());
    limiter.setRelease(apvts.getRawParameterValue("limiterRelease")->load());
    // Note: JUCE Limiter doesn't have setAttack method

    // Check bypass and enable states
    bool isBypassed = apvts.getRawParameterValue("bypass")->load() > 0.5f;
    bool agcEnabled = apvts.getRawParameterValue("agcEnabled")->load() > 0.5f;
    bool gateEnabled = apvts.getRawParameterValue("gateEnabled")->load() > 0.5f;
    bool deEsserEnabled = apvts.getRawParameterValue("deEsserEnabled")->load() > 0.5f;
    bool limiterEnabled = apvts.getRawParameterValue("limiterEnabled")->load() > 0.5f;

    if (!isBypassed)
    {
        // Comprehensive processing chain: AGC → Noise Gate → Compressor → De-esser → Limiter
        juce::dsp::AudioBlock<float> block(buffer);
        juce::dsp::ProcessContextReplacing<float> context(block);

        // 1. AGC Processing (if enabled)
        if (agcEnabled)
        {
            agcProcessor.process(block);
        }

        // 2. Noise Gate Processing (if enabled)
        if (gateEnabled)
        {
            noiseGate.process(context);
            // Update gate activity metering
            gateActivity.store(1.0f); // Simplified metering
        }
        else
        {
            gateActivity.store(0.0f);
        }

        // 3. Compressor Processing (single-band or multiband)
        bool multibandEnabled = apvts.getRawParameterValue("multibandEnabled")->load() > 0.5f;
        if (multibandEnabled)
        {
            multibandCompressor.process(block);
            // Update multiband gain reduction metering
            multibandGR1.store(multibandCompressor.getBandGainReduction(0));
            multibandGR2.store(multibandCompressor.getBandGainReduction(1));
            multibandGR3.store(multibandCompressor.getBandGainReduction(2));
        }
        else
        {
            compressor.process(context);
            // Reset multiband metering when not in use
            multibandGR1.store(0.0f);
            multibandGR2.store(0.0f);
            multibandGR3.store(0.0f);
        }

        // Apply makeup gain
        float makeupGain = juce::Decibels::decibelsToGain(apvts.getRawParameterValue("makeupGain")->load());
        buffer.applyGain(makeupGain);

        // 4. De-esser Processing (if enabled)
        if (deEsserEnabled)
        {
            deEsserProcessor.process(block);
        }

        // 5. Limiter Processing (if enabled)
        if (limiterEnabled)
        {
            limiter.process(context);
        }
    }

    // Calculate output level and comprehensive metering
    float outputRMS = 0.0f;
    for (int channel = 0; channel < totalNumInputChannels; ++channel)
    {
        auto* channelData = buffer.getReadPointer(channel);
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            outputRMS += channelData[sample] * channelData[sample];
        }
    }
    outputRMS = std::sqrt(outputRMS / (buffer.getNumSamples() * totalNumInputChannels));
    outputLevel.store(juce::Decibels::gainToDecibels(outputRMS, -60.0f));

    // Update comprehensive metering
    agcGainReduction.store(agcProcessor.getCurrentGainReduction());
    deEsserReduction.store(deEsserProcessor.getCurrentReduction());

    // Calculate main compressor gain reduction (simplified)
    float makeupGainDb = apvts.getRawParameterValue("makeupGain")->load();
    float grValue = inputLevel.load() - outputLevel.load() + makeupGainDb;
    gainReduction.store(std::max(0.0f, grValue));
}

//==============================================================================
bool CompliAudioProcessor::hasEditor() const
{
    return true; // (change this to false if you choose to not supply an editor)
}

juce::AudioProcessorEditor* CompliAudioProcessor::createEditor()
{
    return new AudioPluginAudioProcessorEditor (*this);
}

//==============================================================================
void CompliAudioProcessor::getStateInformation (juce::MemoryBlock& destData)
{
    // You should use this method to store your parameters in the memory block.
    auto state = apvts.copyState();
    std::unique_ptr<juce::XmlElement> xml (state.createXml());
    copyXmlToBinary (*xml, destData);
}

void CompliAudioProcessor::setStateInformation (const void* data, int sizeInBytes)
{
    // You should use this method to restore your parameters from this memory block,
    std::unique_ptr<juce::XmlElement> xmlState (getXmlFromBinary (data, sizeInBytes));
    if (xmlState.get() != nullptr)
        if (xmlState->hasTagName (apvts.state.getType()))
            apvts.replaceState (juce::ValueTree::fromXml (*xmlState));
}

juce::AudioProcessorValueTreeState::ParameterLayout CompliAudioProcessor::createParameterLayout()
{
    juce::AudioProcessorValueTreeState::ParameterLayout layout;

    // AGC Parameters
    layout.add(std::make_unique<juce::AudioParameterFloat>("agcTargetLevel", "AGC Target Level",
        juce::NormalisableRange<float>(-30.0f, 0.0f, 0.1f, 1.0f), -18.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("agcResponseTime", "AGC Response Time",
        juce::NormalisableRange<float>(100.0f, 5000.0f, 1.0f, 0.3f), 1000.0f));
    layout.add(std::make_unique<juce::AudioParameterBool>("agcEnabled", "AGC Enabled", false));

    // Noise Gate Parameters
    layout.add(std::make_unique<juce::AudioParameterFloat>("gateThreshold", "Gate Threshold",
        juce::NormalisableRange<float>(-60.0f, 0.0f, 0.1f, 1.0f), -40.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("gateAttack", "Gate Attack",
        juce::NormalisableRange<float>(0.1f, 100.0f, 0.1f, 0.3f), 5.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("gateHold", "Gate Hold",
        juce::NormalisableRange<float>(1.0f, 1000.0f, 1.0f, 0.3f), 100.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("gateRelease", "Gate Release",
        juce::NormalisableRange<float>(1.0f, 5000.0f, 1.0f, 0.3f), 200.0f));
    layout.add(std::make_unique<juce::AudioParameterBool>("gateEnabled", "Gate Enabled", false));

    // Compressor parameters - Extended ranges for Custom preset
    layout.add(std::make_unique<juce::AudioParameterFloat>("threshold", "Threshold",
        juce::NormalisableRange<float>(-60.0f, 0.0f, 0.1f, 1.0f), -18.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("ratio", "Ratio",
        juce::NormalisableRange<float>(1.0f, 20.0f, 0.1f, 0.5f), 3.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("attack", "Attack",
        juce::NormalisableRange<float>(0.1f, 3000.0f, 0.1f, 0.3f), 8.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("release", "Release",
        juce::NormalisableRange<float>(1.0f, 5000.0f, 1.0f, 0.3f), 55.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("makeupGain", "Makeup Gain",
        juce::NormalisableRange<float>(-24.0f, 24.0f, 0.1f, 1.0f), 2.5f));

    // Multiband Compressor Parameters
    layout.add(std::make_unique<juce::AudioParameterBool>("multibandEnabled", "Multiband Enabled", false));
    layout.add(std::make_unique<juce::AudioParameterFloat>("lowMidCrossover", "Low-Mid Crossover",
        juce::NormalisableRange<float>(80.0f, 800.0f, 1.0f, 0.3f), 250.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("midHighCrossover", "Mid-High Crossover",
        juce::NormalisableRange<float>(800.0f, 8000.0f, 1.0f, 0.3f), 2500.0f));

    // De-esser Parameters
    layout.add(std::make_unique<juce::AudioParameterFloat>("deEsserFreq", "De-esser Frequency",
        juce::NormalisableRange<float>(4000.0f, 8000.0f, 1.0f, 1.0f), 6000.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("deEsserThreshold", "De-esser Threshold",
        juce::NormalisableRange<float>(-30.0f, 0.0f, 0.1f, 1.0f), -12.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("deEsserReduction", "De-esser Reduction",
        juce::NormalisableRange<float>(1.0f, 12.0f, 0.1f, 1.0f), 6.0f));
    layout.add(std::make_unique<juce::AudioParameterBool>("deEsserEnabled", "De-esser Enabled", false));

    // Enhanced Limiter parameters
    layout.add(std::make_unique<juce::AudioParameterFloat>("limiterThreshold", "Limiter Threshold",
        juce::NormalisableRange<float>(-24.0f, 0.0f, 0.1f, 1.0f), -3.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("limiterAttack", "Limiter Attack",
        juce::NormalisableRange<float>(0.1f, 10.0f, 0.1f, 0.3f), 1.0f));
    layout.add(std::make_unique<juce::AudioParameterFloat>("limiterRelease", "Limiter Release",
        juce::NormalisableRange<float>(1.0f, 1000.0f, 1.0f, 0.3f), 50.0f));
    layout.add(std::make_unique<juce::AudioParameterBool>("limiterEnabled", "Limiter Enabled", true));

    // Preset selection
    juce::StringArray presetChoices = {"Voice Optimized", "Broadcast", "Podcast", "Gaming", "Custom"};
    layout.add(std::make_unique<juce::AudioParameterChoice>("preset", "Preset", presetChoices, 0));

    // Bypass
    layout.add(std::make_unique<juce::AudioParameterBool>("bypass", "Bypass", false));

    return layout;
}


void CompliAudioProcessor::loadPreset(int presetIndex)
{
    if (presetIndex >= 0 && presetIndex < static_cast<int>(presets.size()))
    {
        const auto& preset = presets[presetIndex];

        // Update AGC parameters
        apvts.getParameter("agcTargetLevel")->setValueNotifyingHost(
            apvts.getParameter("agcTargetLevel")->convertTo0to1(preset.agcTargetLevel));
        apvts.getParameter("agcResponseTime")->setValueNotifyingHost(
            apvts.getParameter("agcResponseTime")->convertTo0to1(preset.agcResponseTime));
        apvts.getParameter("agcEnabled")->setValueNotifyingHost(preset.agcEnabled ? 1.0f : 0.0f);

        // Update Noise Gate parameters
        apvts.getParameter("gateThreshold")->setValueNotifyingHost(
            apvts.getParameter("gateThreshold")->convertTo0to1(preset.gateThreshold));
        apvts.getParameter("gateAttack")->setValueNotifyingHost(
            apvts.getParameter("gateAttack")->convertTo0to1(preset.gateAttack));
        apvts.getParameter("gateHold")->setValueNotifyingHost(
            apvts.getParameter("gateHold")->convertTo0to1(preset.gateHold));
        apvts.getParameter("gateRelease")->setValueNotifyingHost(
            apvts.getParameter("gateRelease")->convertTo0to1(preset.gateRelease));
        apvts.getParameter("gateEnabled")->setValueNotifyingHost(preset.gateEnabled ? 1.0f : 0.0f);

        // Update Compressor parameters
        apvts.getParameter("threshold")->setValueNotifyingHost(
            apvts.getParameter("threshold")->convertTo0to1(preset.threshold));
        apvts.getParameter("ratio")->setValueNotifyingHost(
            apvts.getParameter("ratio")->convertTo0to1(preset.ratio));
        apvts.getParameter("attack")->setValueNotifyingHost(
            apvts.getParameter("attack")->convertTo0to1(preset.attack));
        apvts.getParameter("release")->setValueNotifyingHost(
            apvts.getParameter("release")->convertTo0to1(preset.release));
        apvts.getParameter("makeupGain")->setValueNotifyingHost(
            apvts.getParameter("makeupGain")->convertTo0to1(preset.makeupGain));
        apvts.getParameter("multibandEnabled")->setValueNotifyingHost(preset.multibandEnabled ? 1.0f : 0.0f);

        // Update Multiband Crossover parameters
        apvts.getParameter("lowMidCrossover")->setValueNotifyingHost(
            apvts.getParameter("lowMidCrossover")->convertTo0to1(preset.lowMidCrossover));
        apvts.getParameter("midHighCrossover")->setValueNotifyingHost(
            apvts.getParameter("midHighCrossover")->convertTo0to1(preset.midHighCrossover));

        // Update De-esser parameters
        apvts.getParameter("deEsserFreq")->setValueNotifyingHost(
            apvts.getParameter("deEsserFreq")->convertTo0to1(preset.deEsserFreq));
        apvts.getParameter("deEsserThreshold")->setValueNotifyingHost(
            apvts.getParameter("deEsserThreshold")->convertTo0to1(preset.deEsserThreshold));
        apvts.getParameter("deEsserReduction")->setValueNotifyingHost(
            apvts.getParameter("deEsserReduction")->convertTo0to1(preset.deEsserReduction));
        apvts.getParameter("deEsserEnabled")->setValueNotifyingHost(preset.deEsserEnabled ? 1.0f : 0.0f);

        // Update Limiter parameters
        apvts.getParameter("limiterThreshold")->setValueNotifyingHost(
            apvts.getParameter("limiterThreshold")->convertTo0to1(preset.limiterThreshold));
        apvts.getParameter("limiterAttack")->setValueNotifyingHost(
            apvts.getParameter("limiterAttack")->convertTo0to1(preset.limiterAttack));
        apvts.getParameter("limiterRelease")->setValueNotifyingHost(
            apvts.getParameter("limiterRelease")->convertTo0to1(preset.limiterRelease));
        apvts.getParameter("limiterEnabled")->setValueNotifyingHost(preset.limiterEnabled ? 1.0f : 0.0f);
    }
}

void CompliAudioProcessor::updatePresetParameters()
{
    // This method can be called when parameters change to update the preset selection
    // If parameters don't match any preset exactly, switch to "Custom"
    bool matchesPreset = false;

    for (int i = 0; i < 4; ++i) // Check first 4 presets (excluding Custom)
    {
        const auto& preset = presets[i];

        if (std::abs(apvts.getRawParameterValue("threshold")->load() - preset.threshold) < 0.1f &&
            std::abs(apvts.getRawParameterValue("ratio")->load() - preset.ratio) < 0.1f &&
            std::abs(apvts.getRawParameterValue("attack")->load() - preset.attack) < 0.1f &&
            std::abs(apvts.getRawParameterValue("release")->load() - preset.release) < 0.1f &&
            std::abs(apvts.getRawParameterValue("makeupGain")->load() - preset.makeupGain) < 0.1f &&
            std::abs(apvts.getRawParameterValue("limiterThreshold")->load() - preset.limiterThreshold) < 0.1f)
        {
            matchesPreset = true;
            currentPresetIndex = i;
            break;
        }
    }

    if (!matchesPreset)
    {
        currentPresetIndex = 4; // Custom
        apvts.getParameter("preset")->setValueNotifyingHost(
            apvts.getParameter("preset")->convertTo0to1(4.0f));
    }
}

juce::StringArray CompliAudioProcessor::getAvailableInputDevices()
{
    juce::StringArray deviceNames;

    // Use standalone device manager if available, otherwise use processor's device manager
    juce::AudioDeviceManager* deviceMgr = &audioDeviceManager;

#if JucePlugin_Build_Standalone
    if (auto* standaloneHolder = juce::StandalonePluginHolder::getInstance())
    {
        deviceMgr = &standaloneHolder->deviceManager;
    }
#endif

    auto* currentDeviceType = deviceMgr->getCurrentDeviceTypeObject();
    if (currentDeviceType != nullptr)
    {
        currentDeviceType->scanForDevices();
        deviceNames = currentDeviceType->getDeviceNames(true); // true for input devices
    }

    return deviceNames;
}

juce::StringArray CompliAudioProcessor::getAvailableOutputDevices()
{
    juce::StringArray deviceNames;

    // Use standalone device manager if available, otherwise use processor's device manager
    juce::AudioDeviceManager* deviceMgr = &audioDeviceManager;

#if JucePlugin_Build_Standalone
    if (auto* standaloneHolder = juce::StandalonePluginHolder::getInstance())
    {
        deviceMgr = &standaloneHolder->deviceManager;
    }
#endif

    auto* currentDeviceType = deviceMgr->getCurrentDeviceTypeObject();
    if (currentDeviceType != nullptr)
    {
        currentDeviceType->scanForDevices();
        deviceNames = currentDeviceType->getDeviceNames(false); // false for output devices
    }

    return deviceNames;
}

void CompliAudioProcessor::setInputDevice(const juce::String& deviceName)
{
    // Use standalone device manager if available, otherwise use processor's device manager
    juce::AudioDeviceManager* deviceMgr = &audioDeviceManager;

#if JucePlugin_Build_Standalone
    if (auto* standaloneHolder = juce::StandalonePluginHolder::getInstance())
    {
        deviceMgr = &standaloneHolder->deviceManager;
    }
#endif

    auto currentSetup = deviceMgr->getAudioDeviceSetup();
    currentSetup.inputDeviceName = deviceName;

    juce::String error = deviceMgr->setAudioDeviceSetup(currentSetup, true);
    if (error.isNotEmpty())
    {
        DBG("Error setting input device: " + error);
    }
}

void CompliAudioProcessor::setOutputDevice(const juce::String& deviceName)
{
    // Use standalone device manager if available, otherwise use processor's device manager
    juce::AudioDeviceManager* deviceMgr = &audioDeviceManager;

#if JucePlugin_Build_Standalone
    if (auto* standaloneHolder = juce::StandalonePluginHolder::getInstance())
    {
        deviceMgr = &standaloneHolder->deviceManager;
    }
#endif

    auto currentSetup = deviceMgr->getAudioDeviceSetup();
    currentSetup.outputDeviceName = deviceName;

    juce::String error = deviceMgr->setAudioDeviceSetup(currentSetup, true);
    if (error.isNotEmpty())
    {
        DBG("Error setting output device: " + error);
    }
}

juce::String CompliAudioProcessor::getCurrentInputDevice()
{
    // Use standalone device manager if available, otherwise use processor's device manager
    juce::AudioDeviceManager* deviceMgr = &audioDeviceManager;

#if JucePlugin_Build_Standalone
    if (auto* standaloneHolder = juce::StandalonePluginHolder::getInstance())
    {
        deviceMgr = &standaloneHolder->deviceManager;
    }
#endif

    return deviceMgr->getAudioDeviceSetup().inputDeviceName;
}

juce::String CompliAudioProcessor::getCurrentOutputDevice()
{
    // Use standalone device manager if available, otherwise use processor's device manager
    juce::AudioDeviceManager* deviceMgr = &audioDeviceManager;

#if JucePlugin_Build_Standalone
    if (auto* standaloneHolder = juce::StandalonePluginHolder::getInstance())
    {
        deviceMgr = &standaloneHolder->deviceManager;
    }
#endif

    return deviceMgr->getAudioDeviceSetup().outputDeviceName;
}

//==============================================================================
// This creates new instances of the plugin..
juce::AudioProcessor* JUCE_CALLTYPE createPluginFilter()
{
    return new CompliAudioProcessor();
}
