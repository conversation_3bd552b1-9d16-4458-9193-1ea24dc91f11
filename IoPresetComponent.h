#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include "PluginProcessor.h" // Forward declare or include if CompliAudioProcessor specific types are used early

// Forward declaration for processor if full definition not needed here
// class CompliAudioProcessor;

class IoPresetComponent : public juce::Component,
                          public juce::ComboBox::Listener,
                          public juce::Button::Listener
{
public:
    IoPresetComponent(CompliAudioProcessor& proc);
    ~IoPresetComponent() override;

    void paint (juce::Graphics& g) override;
    void resized() override;

    void comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged) override;
    void buttonClicked(juce::Button* button) override;

private:
    void populateInputDeviceList();
    void populateOutputDeviceList();
    void updatePresetInfoLabel();

    CompliAudioProcessor& processorRef;

    juce::Label inputDeviceLabel, outputDeviceLabel, presetLabel, presetInfoLabel;
    juce::ComboBox inputDeviceComboBox, outputDeviceComboBox, presetSelectorComboBox;
    juce::TextButton refreshInputButton, refreshOutputButton; // Will be styled as icons

    std::unique_ptr<juce::AudioProcessorValueTreeState::ComboBoxAttachment> presetAttachment;

    // Store preset info texts, similar to web UI
    std::map<juce::String, juce::String> presetInfoTexts;


    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (IoPresetComponent)
};
